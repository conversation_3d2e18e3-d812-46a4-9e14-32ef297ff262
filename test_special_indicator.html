<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特殊指标识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .special {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .normal {
            background-color: #fff3e0;
            border-color: #ff9800;
        }
        .result {
            font-weight: bold;
            margin-left: 10px;
        }
        .special .result {
            color: #4caf50;
        }
        .normal .result {
            color: #ff9800;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #eee;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>特殊指标识别功能测试</h1>
        
        <h2>测试用例</h2>
        <div id="test-results"></div>
        
        <h2>自定义测试</h2>
        <div>
            <input type="text" id="custom-input" placeholder="请输入检测指标要求" style="width: 300px; padding: 8px;">
            <button onclick="testCustom()" style="padding: 8px 16px; margin-left: 10px;">测试</button>
        </div>
        <div id="custom-result" style="margin-top: 10px;"></div>
    </div>

    <script>
        // 复制优化后的特殊指标识别函数
        function utils_isSpecialIndicator(requirement) {
            if (!requirement || requirement.trim() === '') {
                return false;
            }
            
            requirement = String(requirement).trim();
            
            // 常见的单位模式（只有单位，没有数值）
            const unitOnlyPatterns = [
                /^g\/L$/i,           // g/L
                /^mg\/L$/i,          // mg/L
                /^kg\/m³$/i,         // kg/m³
                /^%$/,               // %
                /^℃$/,               // ℃
                /^°C$/i,             // °C
                /^N$/,               // N
                /^CN$/,              // CN
                /^g$/,               // g
                /^kg$/i,             // kg
                /^ml$/i,             // ml
                /^L$/,               // L
                /^mm$/i,             // mm
                /^cm$/i,             // cm
                /^m$/,               // m
                /^s$/,               // s
                /^min$/i,            // min
                /^h$/,               // h
                /^Pa$/i,             // Pa
                /^MPa$/i,            // MPa
                /^bar$/i,            // bar
                /^ppm$/i,            // ppm
                /^ppb$/i             // ppb
            ];
            
            // 检查是否匹配纯单位模式
            const isUnitOnly = unitOnlyPatterns.some(pattern => pattern.test(requirement));
            
            // 如果是纯单位，则为特殊指标
            if (isUnitOnly) {
                return true;
            }
            
            // 检查是否不包含数字（原有逻辑保持）
            if (!/[0-9]/.test(requirement)) {
                return true;
            }
            
            return false;
        }

        // 测试用例
        const testCases = [
            // 特殊指标（只有单位信息）
            { input: 'g/L', expected: true, description: '密度单位' },
            { input: 'mg/L', expected: true, description: '浓度单位' },
            { input: 'kg/m³', expected: true, description: '密度单位' },
            { input: '%', expected: true, description: '百分比单位' },
            { input: '℃', expected: true, description: '温度单位' },
            { input: '°C', expected: true, description: '温度单位' },
            { input: 'N', expected: true, description: '力单位' },
            { input: 'CN', expected: true, description: '力单位' },
            { input: 'g', expected: true, description: '重量单位' },
            { input: 'kg', expected: true, description: '重量单位' },
            { input: 'ml', expected: true, description: '体积单位' },
            { input: 'L', expected: true, description: '体积单位' },
            { input: 'mm', expected: true, description: '长度单位' },
            { input: 'cm', expected: true, description: '长度单位' },
            { input: 'm', expected: true, description: '长度单位' },
            { input: 'Pa', expected: true, description: '压力单位' },
            { input: 'MPa', expected: true, description: '压力单位' },
            { input: 'bar', expected: true, description: '压力单位' },
            { input: 'ppm', expected: true, description: '浓度单位' },
            { input: 'ppb', expected: true, description: '浓度单位' },
            
            // 原有特殊情况
            { input: '暂无', expected: true, description: '暂无标识' },
            { input: '/', expected: true, description: '斜杠标识' },
            { input: '全通过', expected: true, description: '全通过标识' },
            { input: '无黑点杂质', expected: true, description: '无杂质标识' },
            { input: '白色粉末', expected: true, description: '不含数字的描述' },
            
            // 常规指标（有数值范围）
            { input: '≥85%', expected: false, description: '大于等于85%' },
            { input: '≤10mg/L', expected: false, description: '小于等于10mg/L' },
            { input: '50-100g/L', expected: false, description: '50-100g/L范围' },
            { input: '25±2℃', expected: false, description: '25±2℃范围' },
            { input: '>5N', expected: false, description: '大于5N' },
            { input: '<0.5%', expected: false, description: '小于0.5%' },
            { input: '100kg', expected: false, description: '100kg固定值' },
            { input: '1.5-2.0g/cm³', expected: false, description: '密度范围' }
        ];

        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = utils_isSpecialIndicator(testCase.input);
                const passed = result === testCase.expected;
                
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-case ${result ? 'special' : 'normal'}`;
                testDiv.innerHTML = `
                    <strong>测试 ${index + 1}:</strong> "${testCase.input}" - ${testCase.description}
                    <span class="result">${result ? '特殊指标' : '常规指标'}</span>
                    ${passed ? '✅' : '❌ 预期: ' + (testCase.expected ? '特殊指标' : '常规指标')}
                `;
                resultsDiv.appendChild(testDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.style.marginTop = '20px';
            summaryDiv.style.padding = '15px';
            summaryDiv.style.backgroundColor = passCount === totalCount ? '#e8f5e8' : '#ffebee';
            summaryDiv.style.borderRadius = '4px';
            summaryDiv.innerHTML = `
                <h3>测试结果总结</h3>
                <p>通过: ${passCount}/${totalCount} (${(passCount/totalCount*100).toFixed(1)}%)</p>
                ${passCount === totalCount ? '<p style="color: green;">✅ 所有测试通过！</p>' : '<p style="color: red;">❌ 部分测试失败</p>'}
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        function testCustom() {
            const input = document.getElementById('custom-input').value;
            const resultDiv = document.getElementById('custom-result');
            
            if (!input.trim()) {
                resultDiv.innerHTML = '<p style="color: red;">请输入测试内容</p>';
                return;
            }

            const result = utils_isSpecialIndicator(input);
            resultDiv.innerHTML = `
                <div class="test-case ${result ? 'special' : 'normal'}">
                    <strong>输入:</strong> "${input}"
                    <span class="result">${result ? '特殊指标' : '常规指标'}</span>
                </div>
            `;
        }

        // 页面加载完成后运行测试
        window.onload = function() {
            runTests();
        };
    </script>
</body>
</html>
