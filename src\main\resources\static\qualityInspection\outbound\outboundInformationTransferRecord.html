<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>出库传递单-尚舜化工</title>
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../picture/logo.png">
    <style>
        [v-cloak] {
            display: none;
        }

        /* 字体设置 */
        @font-face {
            font-family: "zad";
            src: url("../../font/MiSans-Medium.woff2");
        }

        /* 字体应用组件 */
        html,
        body,
        button,
        input,
        select,
        textarea,
        form {
            font-family: "zad", sans-serif !important;
        }

        .dataEditInput .el-input__inner {
            text-align: center !important;
            font-size: 12px !important;
        }

        .inspectionDataEditInput .el-input__inner {
            text-align: center !important;
            font-size: 12px !important;
        }

        /* 防止滚动条遮挡表格内容 */
        .el-table__body-wrapper {
            z-index: 2;
        }

        .el-table__fixed {
            height: 100% !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
        }

        .maxIndex1 .el-input__inner {
            background-color: #FADADD;
            /* 低值的背景颜色 */
        }

        .requirementTable .el-table__body-wrapper {
            overflow-x: hidden !important;
        }

        /* 跑马灯指示器样式 */
        .el-carousel__indicators--outside button.el-carousel__button {
            background-color: #409EFF;
            /* 未激活的指示器颜色 */
            width: 30px;
            /* 指示器宽度 */
            border-radius: 6px;
            /* 圆角 */
        }

        /* 跑马灯当前激活的指示器样式 */
        .el-carousel__indicators--outside .el-carousel__indicator.is-active button {
            background-color: #1E90FF;
            /* 激活的指示器颜色 */
        }

        /* 跑马灯箭头样式 */
        .el-carousel__arrow {
            background-color: rgba(31, 45, 61, 0.5);
            /* 箭头背景颜色 */
            color: #fff;
            /* 箭头颜色 */
            border-radius: 50%;
            /* 圆形箭头 */
            width: 36px;
            /* 箭头宽度 */
            height: 36px;
            /* 箭头高度 */
        }

        /* 跑马灯箭头悬停样式 */
        .el-carousel__arrow:hover {
            background-color: rgba(31, 45, 61, 0.8);
            /* 箭头悬停背景颜色 */
        }

        .el-dropdown-menu {
            min-width: 120px !important;
            /* 设置最小宽度 */
            padding: 5px 0 !important;
            /* 调整内边距 */
        }

        .el-dropdown-menu__item {
            font-size: 14px !important;
            /* 调整字体大小 */
            line-height: 40px !important;
            /* 调整行高 */
            padding: 0 15px !important;
            /* 调整选项内边距 */
        }

        .el-carousel {
            transition: height 0.3s ease;
        }

        @keyframes blink {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.3;
            }

            100% {
                opacity: 1;
            }
        }

        .blink-icon {
            animation: blink 0.7s infinite;
        }

        .el-notification {
            z-index: 99999 !important;
        }

        .wide-notification {
            width: 400px !important;
            /* 设置通知宽度为300px */
        }

        /* 如果需要调整消息文本的样式 */
        .wide-notification .el-notification__content {
            text-align: left;
            word-break: break-all;
            margin: 5px 0;
        }

        /* 添加历史数据行的背景色 */
        .history-text {
            color: #FF0000 !important;
        }
    </style>
</head>

<body class="hold-transition">
    <div id="app" v-loading.fullscreen.lock="fullscreenLoading" v-cloak>
        <!--标题-->
        <div class="content-header">
            <h1>出库传递单</h1>
        </div>
        <!--内容-->
        <div class="app-container">
            <div class="box">
                <br>
                <!--传递单查询-->
                <div>
                    <el-row :gutter="20">
                        <!--所属公司-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_transferRecordQuery.linkId" filterable multiple
                                collapse-tags default-first-option :reserve-keyword="true" placeholder="请选择公司"
                                :style="{width: '100%'}" value="" @change="main_getTransferRecordList()" clearable>
                                <el-option v-for="item in main_linkIdOptionList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--处理状态-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_transferRecordQuery.handleStatus"
                                placeholder="请选择处理状态" :style="{width: '100%'}" value=""
                                @change="main_getTransferRecordList()" clearable>
                                <el-option v-for="item in main_handleStatusOptionList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--初审状态-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_transferRecordQuery.firstReviewStatus"
                                placeholder="请选择初审状态" :style="{width: '100%'}" value=""
                                @change="main_getTransferRecordList()" clearable>
                                <el-option v-for="item in main_firstReviewStatusOptionList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--复审状态-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_transferRecordQuery.secondReviewStatus"
                                placeholder="请选择复审状态" :style="{width: '100%'}" value=""
                                @change="main_getTransferRecordList()" clearable>
                                <el-option v-for="item in main_secondReviewStatusOptionList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <!--操作按钮-->
                        <el-col :span="8">
                            <el-tooltip content="查询出库传递单" placement="top">
                                <el-button size="small" @click="main_getTransferRecordList()" type="primary"
                                    icon="el-icon-search" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip content="清空查询条件" placement="top">
                                <el-button size="small" @click="main_refreshSearch()" type="primary"
                                    icon="el-icon-refresh" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip v-if="user.userPermission && user.userPermission.transportReportHandle"
                                content="设置工作人员" placement="top">
                                <el-button size="small" @click="main_openStaffOptionDialog()" type="warning"
                                    icon="el-icon-user-solid" circle>
                                </el-button>
                            </el-tooltip>
                            <el-tooltip v-if="user.id === 79 || user.id === 610" content="导出随车质检单数据" placement="top">
                                <el-button size="small" @click="main_openExportDialog()" type="success"
                                    icon="el-icon-download" circle>
                                </el-button>
                            </el-tooltip>
                        </el-col>
                    </el-row>
                    <br>
                    <el-row :gutter="20">
                        <!--客户名称-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入客户名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_transferRecordQuery.customerName"
                                @change="main_getTransferRecordList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--产品名称-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入产品名称" clearable :style="{width: '100%'}"
                                v-model.trim="main_transferRecordQuery.productName"
                                @change="main_getTransferRecordList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--生产批号-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入生产批号" clearable :style="{width: '100%'}"
                                v-model.trim="main_transferRecordQuery.productionBatch"
                                @change="main_getTransferRecordList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--质检批号-->
                        <el-col :span="3">
                            <el-input size="small" placeholder="请输入质检批号" clearable :style="{width: '100%'}"
                                v-model.trim="main_transferRecordQuery.qualityInspectionBatch"
                                @change="main_getTransferRecordList()" prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <!--开单状态-->
                        <el-col :span="3">
                            <el-select size="small" v-model="main_transferRecordQuery.onlineAvailable"
                                placeholder="请选择开单状态" :style="{width: '100%'}" value=""
                                @change="main_getTransferRecordList()" clearable>
                                <el-option v-for="item in main_onlineAvailableOptionList" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <el-date-picker size="small" placement="bottom-start" unlink-panels
                                v-model="main_transferRecordQuery.queryDate" type="daterange" start-placeholder="开始日期"
                                end-placeholder="结束日期" value-format="yyyy-MM-dd" :style="{width: '100%'}"
                                @change="main_getTransferRecordList()" clearable>
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </div>
                <br>
                <!--传递单列表-->
                <div>
                    <!--提单表格-->
                    <el-table :row-style="{height: '50px'}" v-loading="main_tableLoading" :cell-style="{padding: '0px'}"
                        :data="main_transferRecordList" highlight-current-row
                        :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                        <el-table-column fixed type="index" align="center" width="50">
                        </el-table-column>
                        <el-table-column prop="warehouseName" label="仓库名称" align="center" width="120"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="customerName" label="客户名称" align="center" width="250"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="productName" label="产品名称" align="center" width="200"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="totalWeight" label="数量(吨)" align="center" width="100"
                            show-overflow-tooltip>
                        </el-table-column>
                         <!--批号信息-->
                         <el-table-column label="批号信息" align="center" width="120" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-popover v-if="scope.row.batchInfo && scope.row.batchInfo.length > 0"
                                    placement="top" width="400" trigger="hover">
                                    <el-table :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                        :data="sortBatchInfo(scope.row.batchInfo)" size="mini" border
                                        :span-method="(params) => batchInfoSpanMethod(params, sortBatchInfo(scope.row.batchInfo))">
                                        <el-table-column prop="productionBatch" label="生产批号" min-width="100">
                                        </el-table-column>
                                        <el-table-column prop="qualityInspectionBatch" label="质检批号" min-width="100">
                                        </el-table-column>
                                    </el-table>
                                    <i slot="reference" class="el-icon-document"
                                        style="cursor: pointer; color: #409EFF;"></i>
                                </el-popover>
                                <span v-else>-</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="outboundDate" label="单据日期" align="center" width="120"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="ourContractNumber" label="我方合同号" align="center" width="120"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="theirContractNumber" label="对方合同号" align="center" width="120"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="theirContractDetail" label="对方合同号明细" align="center" width="120"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="addUserName" label="操作人" align="center" width="100"
                            show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column prop="addTime" label="操作时间" align="center" width="200" show-overflow-tooltip>
                        </el-table-column>
                        <el-table-column fixed="right" prop="firstReviewStatus" label="初审" align="center" width="70"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <i v-if="scope.row.firstReviewStatus" class="el-icon-success"
                                    style="color: green; font-size: 24px"></i>
                                <i v-if="scope.row.firstReviewStatus === false" class="el-icon-error"
                                    style="color: red; font-size: 24px"></i>
                                <i v-if="scope.row.firstReviewStatus === null" class="el-icon-remove"
                                    style="color: gray; font-size: 24px"></i>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" prop="secondReviewStatus" label="复审" align="center" width="70"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <i v-if="scope.row.secondReviewStatus" class="el-icon-success"
                                    style="color: green; font-size: 24px"></i>
                                <i v-if="scope.row.secondReviewStatus === false" class="el-icon-error"
                                    style="color: red; font-size: 24px"></i>
                                <i v-if="scope.row.secondReviewStatus === null" class="el-icon-remove"
                                    style="color: gray; font-size: 24px"></i>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" prop="onlineAvailable" label="线上" align="center" width="70"
                            show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-tooltip :content="getOnlineAvailableTooltip(scope.row.onlineAvailable)"
                                    placement="top">
                                    <i v-if="scope.row.onlineAvailable === 1" class="el-icon-success"
                                        style="color: green; font-size: 24px; cursor: pointer"
                                        @click="main_changeOnlineAvailable(scope.row)"></i>
                                    <i v-if="scope.row.onlineAvailable === 0" class="el-icon-error"
                                        style="color: red; font-size: 24px; cursor: pointer"
                                        @click="main_changeOnlineAvailable(scope.row)"></i>
                                    <i v-if="scope.row.onlineAvailable === 2" class="el-icon-warning"
                                        style="color: #E6A23C; font-size: 24px; cursor: pointer"
                                        @click="main_changeOnlineAvailable(scope.row)"></i>
                                    <i v-if="scope.row.onlineAvailable === 3" class="el-icon-warning blink-icon"
                                        style="color: #E6A23C; font-size: 24px; cursor: pointer"
                                        @click="main_changeOnlineAvailable(scope.row)"></i>
                                    <i v-if="scope.row.onlineAvailable === null" class="el-icon-remove"
                                        style="color: gray; font-size: 24px; cursor: pointer"
                                        @click="main_changeOnlineAvailable(scope.row)"></i>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" align="center" width="240" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-tooltip content="查看出库传递单详情" placement="top">
                                    <el-button type="primary" size="mini"
                                        @click="main_openTransferRecordDetailsDialog(scope.row)" icon="el-icon-search"
                                        circle></el-button>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.handleStatus" content="预览随车质检单" placement="top">
                                    <el-button type="primary" size="mini"
                                        @click="main_openTransportReportPreviewDialog(scope.row)" icon="el-icon-picture"
                                        circle></el-button>
                                </el-tooltip>
                                <!-- <el-tooltip v-if="scope.row.secondReviewStatus" content="下载随车质检单" placement="top">
                                    <el-button type="success" size="mini"
                                        @click="main_downloadTransportReport(scope.row)" icon="el-icon-download"
                                        circle></el-button>
                                </el-tooltip> -->
                                <el-tooltip
                                    v-if="scope.row.secondReviewStatus && (user.userPermission && user.userPermission.transportReportDownload)"
                                    content="下载随车质检单" placement="top">
                                    <el-dropdown size="mini"
                                        @command="(command) => main_downloadTransportReport(scope.row, command)"
                                        trigger="click">
                                        <el-button style="margin-left: 10px" type="success" size="mini"
                                            icon="el-icon-download" circle></el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item command="zh">中文版</el-dropdown-item>
                                            <el-dropdown-item command="en">英文版</el-dropdown-item>
                                            <el-dropdown-item command="zh_en">中英文版</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </el-tooltip>
                                <el-tooltip
                                    v-if="scope.row.handleStatus && (user.userPermission && user.userPermission.transportReportHandle)"
                                    content="重置随车质检单" placement="top">
                                    <el-button style="margin-left: 10px" type="warning" size="mini"
                                        @click="main_resetTransportReport(scope.row)" icon="el-icon-refresh-right"
                                        circle></el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页组件-->
                    <el-pagination small style="text-align: right;" layout="prev, pager, next"
                        @current-change="main_handleCurrentChange" :current-page="main_transferRecordQuery.current"
                        :page-size="main_transferRecordQuery.size" :total="main_transferRecordQuery.total">
                    </el-pagination>
                </div>
                <!--传递单详情窗口-->
                <div>
                    <el-dialog :visible.sync="details_transferRecordDetailsVisible" width="90%" top="5vh" center>
                        <template slot="title">
                            <div>
                                <span>{{ details_transferRecordTitle.split("-")[0] }}</span>
                                <el-divider direction="vertical"></el-divider>
                                <span>{{ details_transferRecordTitle.slice(details_transferRecordTitle.indexOf('-') + 1)
                                    }}</span>
                            </div>
                        </template>
                        <div>
                            <el-divider>检测指标</el-divider>
                            <el-table size="small" :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                :data="details_transferRecordDetails.requirementData" highlight-current-row
                                :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                <el-table-column
                                    v-for="(item, index) in details_transferRecordDetails.requirementColumns"
                                    :key="index" :label="item.label" align="center" :property="item.label" resizable
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                            </el-table>
                        </div>
                        <el-divider>检测数据</el-divider>
                        <div v-for="(item, index) in details_transferRecordDetails.productionAndQualityDataList"
                            :key="item.transferRecordList[0].outboundProductionBatch">
                            <el-table :ref="item.transferRecordList[0].outboundProductionBatch" size="small"
                                :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                :data="item.qualityInspectionData" highlight-current-row
                                :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                @selection-change="details_handleSelectionChange(item, $event)"
                                :span-method="(params) => details_spanMethod(params, item.qualityInspectionData)"
                                border>
                                <el-table-column type="selection" width="50">
                                </el-table-column>
                                <el-table-column label="生产批号" property="production_productionBatch" align="center"
                                    resizable width="120" fixed :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="吨数" property="production_weight" align="center" resizable fixed
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="规格" property="production_productionPackagingSpecification"
                                    align="center" resizable fixed :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="袋数" property="production_quantity" align="center" resizable
                                    fixed :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="质检批号" property="qualityInspectionBatch" align="center" resizable
                                    width="120" :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="发货数量" property="outboundQuantity" align="center" resizable
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="检测数量" property="quantity" align="center" resizable
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column
                                    v-for="(item, index) in details_transferRecordDetails.requirementColumns"
                                    :key="index" :label="item.label" align="center" :property="item.label" resizable
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column label="结论" property="result" align="center" resizable
                                    :show-overflow-tooltip=true>
                                </el-table-column>
                                <el-table-column fixed="right" label="抽检数据" align="center" resizable>
                                    <template slot-scope="scope">
                                        <el-popover v-if="scope.row.samplingData && scope.row.samplingData.length > 0"
                                            placement="top" width="400" trigger="hover">
                                            <el-table :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                                :data="scope.row.samplingData" size="mini" border>
                                                <el-table-column prop="matchingName" label="抽检项目" min-width="100">
                                                </el-table-column>
                                                <el-table-column prop="value" label="检测数据" min-width="100">
                                                    <template slot-scope="props">
                                                        {{ props.row.value || '-' }}
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                            <i slot="reference" class="el-icon-search"
                                                style="cursor: pointer; color: #409EFF;"></i>
                                        </el-popover>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <br v-if="index < details_transferRecordDetails.productionAndQualityDataList.length -1">
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip
                                v-if="!details_currentTransferRecord.handleStatus&&(user.userPermission && user.userPermission.transportReportHandle)"
                                content="创建随车质检单" placement="top">
                                <el-button type="success" size="small" @click="details_creatTransportReport()"
                                    icon="el-icon-document-add" circle></el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
                <!--随车质检单详情窗口-->
                <div>
                    <el-dialog :visible.sync="report_transportReportDetailsVisible" title="随车质检单详情" width="75%"
                        top="8vh" center>
                        <el-form ref="report_formRef" size="small" label-width="80px">
                            <!--基础信息-->
                            <div>
                                <el-row :gutter="20">
                                    <!--订货单位-->
                                    <el-col :span="12">
                                        <el-form-item label="订货单位">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入订货单位"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.customerName)"
                                                v-model.trim="report_transportQualityInspectionReport.customerName">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!--数量-->
                                    <el-col :span="12">
                                        <el-form-item label="数量">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入数量"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.weightInTons)"
                                                v-model.trim="report_transportQualityInspectionReport.weightInTons">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <!--产品名称-->
                                    <el-col :span="12">
                                        <el-form-item label="产品名称">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入产品名称"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.productName)"
                                                v-model.trim="report_transportQualityInspectionReport.productName">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!--规格-->
                                    <el-col :span="12">
                                        <el-form-item label="规格">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入规格"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.packagingSpecifications)"
                                                v-model.trim="report_transportQualityInspectionReport.packagingSpecifications">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <!--执行标准-->
                                    <el-col :span="24">
                                        <el-form-item label="执行标准">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入执行标准"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.executionStandard)"
                                                v-model.trim="report_transportQualityInspectionReport.executionStandard">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                            <!--检测指标-->
                            <div>
                                <el-table class="requirementTable" ref="report_requirementDataTableRef" size="small"
                                    :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                    :data="report_requirementData" highlight-current-row
                                    :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                    <el-table-column align="center" resizable width="175" fixed>
                                    </el-table-column>
                                    <el-table-column align="center" resizable width="175" fixed>
                                    </el-table-column>
                                    <el-table-column v-for="(item, index) in report_requirementColumns" :key="index"
                                        width="150" :label="item.label" align="center" :property="item.label" resizable>
                                    </el-table-column>
                                    <el-table-column align="center" resizable fixed="right">
                                    </el-table-column>
                                </el-table>
                            </div>
                            <br>
                            <!--检测数据-->
                            <div>
                                <el-table ref="report_inspectionDataTableRef" size="small" :row-style="{height: '35px'}"
                                    :cell-style="{padding: '0px'}" :data="report_qualityInspectionData"
                                    highlight-current-row :header-cell-style="{background: '#f2f4f7', color: '#606266'}"
                                    border>
                                    <el-table-column label="生产批号" property="productionBatch" align="center" resizable
                                        width="175" fixed :show-overflow-tooltip=true>
                                        <template slot-scope="scope">
                                            <span :class="{'history-text': scope.row.isHistory}">
                                                {{ scope.row.productionBatch }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="生产日期" property="productionDate" align="center" resizable
                                        width="175" fixed>
                                        <template slot-scope="scope">
                                            <el-input class="inspectionDataEditInput" size="small"
                                                :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(scope.row.productionDate)"
                                                v-model="scope.row.productionDate">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <!--检测数据-->
                                    <el-table-column v-for="(item, index) in report_transportReportColumns" :key="index"
                                        :label="item.label" align="center" :property="item.label" resizable width="150">
                                        <template slot-scope="scope">
                                            <el-input class="inspectionDataEditInput" size="small"
                                                :style="{width: '100%'}" v-model="scope.row[item.label]"
                                                :class="report_getBackgroundColorClass(scope.row[item.label], scope.column.label)"
                                                @change="report_resultJudgment(scope.row[item.label], scope.column.label)">
                                            </el-input>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="数量" property="quantity" align="center" resizable
                                        fixed="right" :show-overflow-tooltip=true>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <br>
                            <div>
                                <el-row :gutter="20">
                                    <!--检测结论-->
                                    <el-col :span="24">
                                        <el-form-item label="检测结论">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入检测结论"
                                                clearable :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.inspectionConclusion)"
                                                v-model.trim="report_transportQualityInspectionReport.inspectionConclusion">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20">
                                    <!--主检-->
                                    <el-col :span="6">
                                        <el-form-item label="主检">
                                            <el-select class="informationEditInput" size="small"
                                                v-model="report_transportQualityInspectionReport.inspector"
                                                placeholder="请选择检测人"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.inspector)"
                                                :style="{width: '100%'}" value="">
                                                <el-option v-for="item in report_inspectorLabelList" :key="item.value"
                                                    :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <!--校核-->
                                    <el-col :span="6">
                                        <el-form-item label="校核">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入校核人"
                                                :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.auditor)"
                                                v-model.trim="report_transportQualityInspectionReport.auditor">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!--审核-->
                                    <el-col :span="6">
                                        <el-form-item label="审核">
                                            <el-input class="informationEditInput" size="small" placeholder="请输入审核人"
                                                :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.verifier)"
                                                v-model.trim="report_transportQualityInspectionReport.verifier">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <!--出单日期-->
                                    <el-col :span="6">
                                        <el-form-item label="出单日期">
                                            <el-date-picker class="informationEditInput" size="small"
                                                :style="{width: '100%'}"
                                                :class="report_getCommonBackgroundColorClass(report_transportQualityInspectionReport.reportDate)"
                                                v-model="report_transportQualityInspectionReport.reportDate" type="date"
                                                value-format="yyyy-MM-dd" placeholder="请选择日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-form>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="保存随车质检单" placement="top">
                                <el-button type="success" size="small" @click="report_saveTransportReport()"
                                    icon="el-icon-check" circle></el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
                <!--随车质检单预览窗口-->
                <div>
                    <el-dialog :visible.sync="preview_transportReportPreviewVisible" append-to-body title="随车质检单预览"
                        width="90%" top="5vh" center @closed="handlePreviewDialogClose" class="preview-dialog">
                        <div>
                            <el-carousel ref="previewCarousel" :height="carouselHeight" arrow="always"
                                indicator-position="outside" :autoplay="false" :initial-index="0" trigger="click"
                                @change="handleCarouselChange">
                                <el-carousel-item v-for="(url, index) in preview_transportReportPreviewUrls"
                                    :key="index">
                                    <el-image :src="url" fit="contain" style="width: 100%" ref="previewImages"
                                        @load="onImageLoad($event, index)">
                                    </el-image>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip
                                v-if="preview_currentTransferRecord.firstReviewStatus === null && (user.userPermission && user.userPermission.transportReportAudit)"
                                content="初审通过" placement="top">
                                <el-button type="success" size="small" @click="preview_reportReview(1, true)"
                                    icon="el-icon-check" circle></el-button>
                            </el-tooltip>
                            <el-tooltip
                                v-if="preview_currentTransferRecord.firstReviewStatus === null && (user.userPermission && user.userPermission.transportReportAudit)"
                                content="初审否决" placement="top">
                                <el-button type="danger" size="small" @click="preview_reportReview(1, false)"
                                    icon="el-icon-close" circle></el-button>
                            </el-tooltip>
                            <el-tooltip
                                v-if="preview_currentTransferRecord.firstReviewStatus && (user.userPermission && user.userPermission.transportReportAudit)"
                                content="复审通过" placement="top">
                                <el-button type="success" size="small" @click="preview_reportReview(2, true)"
                                    icon="el-icon-check" circle></el-button>
                            </el-tooltip>
                            <el-tooltip
                                v-if="preview_currentTransferRecord.firstReviewStatus && (user.userPermission && user.userPermission.transportReportAudit)"
                                content="复审否决" placement="top">
                                <el-button type="danger" size="small" @click="preview_reportReview(2, false)"
                                    icon="el-icon-close" circle></el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
                <!--工作人员设置窗口-->
                <div>
                    <el-dialog :visible.sync="main_staffOptionVisible" title="工作人员设置" width="50%" top="10vh" center>
                        <el-row :gutter="20">
                            <!--所属公司-->
                            <el-col :span="12">
                                <el-input size="small" placeholder="请输入校核人" clearable :style="{width: '100%'}"
                                    v-model.trim="staff.auditor1" prefix-icon="el-icon-user">
                                </el-input>
                            </el-col>
                            <el-col :span="12">
                                <el-input size="small" placeholder="请输入审核人" clearable :style="{width: '100%'}"
                                    v-model.trim="staff.auditor2" prefix-icon="el-icon-user">
                                </el-input>
                            </el-col>
                        </el-row>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="保存工作人员" placement="top">
                                <el-button type="success" size="small" @click="main_setStaff()" icon="el-icon-check"
                                    circle></el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
                <!--客户不匹配提示窗口-->
                <div>
                    <el-dialog :visible.sync="customerMismatch_dialogVisible" title="客户不匹配提示" width="60%" top="10vh"
                        center :close-on-click-modal="false" :close-on-press-escape="false">
                        <div>
                            <el-table size="small" :row-style="{height: '35px'}" :cell-style="{padding: '0px'}"
                                :data="customerMismatch_tableData" highlight-current-row
                                :header-cell-style="{background: '#f2f4f7', color: '#606266'}" border>
                                <el-table-column prop="qualityInspectionBatch" label="质检批号" align="center"
                                    min-width="120" show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="qualityCustomer" label="质检客户" align="center" min-width="180"
                                    show-overflow-tooltip>
                                </el-table-column>
                                <el-table-column prop="outboundCustomer" label="发货客户" align="center" min-width="180"
                                    show-overflow-tooltip>
                                </el-table-column>
                            </el-table>
                            <br>
                            <div style="margin-top: 15px; color: #E6A23C; text-align: center;">
                                <i class="el-icon-warning"></i> 发货客户与质检客户不匹配，请确认检测结果是否合格。
                            </div>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-tooltip content="已知晓，继续操作" placement="top">
                                <el-button type="primary" size="small" @click="customerMismatch_confirm()"
                                    icon="el-icon-check" circle></el-button>
                            </el-tooltip>
                        </span>
                    </el-dialog>
                </div>
                <!--随车质检单数据导出窗口-->
                <div>
                    <el-dialog :visible.sync="export_dialogVisible" title="随车质检单数据导出" width="60%" top="10vh" center>
                        <el-form :model="export_form" label-width="120px" size="small">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="集团选择">
                                        <el-select v-model="export_form.groupNumber" placeholder="请选择集团"
                                                   clearable filterable :style="{width: '100%'}"
                                                   :filter-method="export_filterGroupOptions"
                                                   @change="export_onGroupChange">
                                            <el-option v-for="item in export_filteredGroupOptions" :key="item.value"
                                                       :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="客户选择">
                                        <el-select v-model="export_form.customerNumbers" placeholder="请选择客户"
                                                   multiple collapse-tags clearable filterable remote
                                                   :remote-method="export_searchCustomers"
                                                   :loading="export_customerLoading"
                                                   :reserve-keyword="true"
                                                   :style="{width: '100%'}">
                                            <el-option v-for="item in export_customerOptions" :key="item.value"
                                                       :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="产品类别">
                                        <el-select v-model="export_form.productCategoryNumbers" placeholder="请选择产品类别"
                                                   multiple collapse-tags clearable filterable :style="{width: '100%'}">
                                            <el-option v-for="item in export_productCategoryOptions" :key="item.value"
                                                       :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="时间范围">
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <el-date-picker v-model="export_form.dateRange"
                                                            :type="export_datePickerType"
                                                            :start-placeholder="export_datePickerType === 'daterange' ? '开始日期' : '开始月份'"
                                                            :end-placeholder="export_datePickerType === 'daterange' ? '结束日期' : '结束月份'"
                                                            :value-format="export_datePickerType === 'daterange' ? 'yyyy-MM-dd' : 'yyyy-MM'"
                                                            :style="{width: 'calc(100% - 40px)'}"
                                                            clearable>
                                            </el-date-picker>
                                            <el-button type="primary" size="small" icon="el-icon-refresh"
                                                       @click="export_toggleDatePickerType" circle
                                                       :title="export_datePickerType === 'daterange' ? '切换到月份选择' : '切换到日期选择'">
                                            </el-button>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24">
                                    <el-form-item label="文件设置">
                                        <el-checkbox v-model="export_form.separateByCustomer">
                                            按客户区分Excel文件
                                        </el-checkbox>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <span slot="footer" class="dialog-footer">
                            <el-button type="info" size="small" @click="export_dialogVisible = false" icon="el-icon-close" circle></el-button>
                            <el-button type="success" size="small" @click="export_executeExport" :loading="export_loading"
                                       icon="el-icon-download" circle>
                                <!-- {{ export_loading ? '导出中...' : '确定导出' }} -->
                            </el-button>
                        </span>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</body>
<!-- 引入组件库 -->
<script src="../../js/vue.js"></script>
<script src="../../js/index.js"></script>
<script src="../../js/axios.min.js"></script>
<script type="text/javascript" src="../../js/jquery.min.js"></script>
<link rel="stylesheet" href="../../element-ui/lib/theme-chalk/index.css">
<script>
    let vue = new Vue({
        el: '#app',
        data: {
            /*通用*/
            fullscreenLoading: false,// 全屏加载
            user: "",// 当前用户
            staff: "",
            defaultTime: ['00:00:00', '23:59:59'],

            /*传递单列表*/
            main_transferRecordQuery: {
                current: 1,//当前页码
                size: 10,//每页显示的记录数
                total: 0,//总记录数

                linkId: [],
                customerName: "",
                productName: "",
                productionBatch: "", // 添加生产批号查询
                qualityInspectionBatch: "", // 添加质检批号查询
                handleStatus: "",
                firstReviewStatus: "",
                secondReviewStatus: "",

                queryDate: ["", ""],// 查询日期（无需接收）
                startDate: "",// 起始日期
                endDate: "",// 截止日期
            },// 传递单查询
            main_transferRecordList: [],// 传递单列表
            main_staffOptionVisible: false,
            main_tableLoading: false,

            /*传递单详情*/
            details_currentTransferRecord: "",// 当前传递单
            details_transferRecordDetails: "",// 传递单详情
            details_transportReportDataList: [],// 随车质检单数据集合
            details_transferRecordDetailsVisible: false,// 传递单详情窗口
            details_transferRecordTitle: "",// 传递单详情窗口标题

            /*随车质检单*/
            report_transportQualityInspectionReport: "",// 随车质检单实体类
            report_requirementColumns: [],// 随车质检单表头
            report_requirementData: [],// 随车质检单表头
            report_transportReportColumns: [],// 随车质检单表头
            report_qualityInspectionData: [],// 随车质检单质检信息
            report_transportReportDetailsVisible: false,// 随车质检单详情窗口可见性
            report_inspectionConclusion: true,

            /*客户不匹配dialog*/
            customerMismatch_dialogVisible: false,
            customerMismatch_tableData: [],
            customerMismatch_transportReportCreatDTO: null,

            /*随车质检单预览*/
            preview_transportReportPreviewVisible: false,
            preview_transportReportPreviewUrl: "",// 随车质检单预览url
            preview_transportReportPreviewUrls: [],
            preview_currentTransferRecord: "",// 当前传递单

            /*选项*/
            main_linkIdOptionList: [],// 公司选项列表
            main_handleStatusOptionList: [],// 处理状态选项列表
            main_firstReviewStatusOptionList: [],// 初审状态选项列表
            main_secondReviewStatusOptionList: [],// 复审状态选项列表
            main_onlineAvailableOptionList: [],// 开单状态选项列表
            report_inspectorLabelList: [],// 检测人选项列表

            carouselHeight: '1000px',  // 默认高度
            imageHeights: [], // 存储每张图片的高度
            currentImageIndex: 0, // 当前显示的图片索引

            /*导出功能*/
            export_dialogVisible: false, // 导出对话框可见性
            export_loading: false, // 导出加载状态
            export_customerLoading: false, // 客户搜索加载状态
            export_form: {
                groupNumber: '', // 集团编号
                customerNumbers: [], // 客户编号列表
                productCategoryNumbers: [], // 产品类别编号列表
                dateRange: [], // 时间范围
                separateByCustomer: false // 是否按客户区分Excel文件
            },
            export_groupOptions: [], // 集团选项
            export_filteredGroupOptions: [], // 过滤后的集团选项
            export_customerOptions: [], // 客户选项
            export_productCategoryOptions: [], // 产品类别选项
            export_datePickerType: 'daterange' // 日期选择器类型：'daterange' 或 'monthrange'
        },

        created() {
            let url = decodeURI(location.search).slice(1);
            // 创建空对象存储参数
            let obj = {};
            // 再通过 & 将每一个参数单独分割出来
            let paramsArr = url.split('&')
            for (let i = 0, len = paramsArr.length; i < len; i++) {
                // 再通过 = 将每一个参数分割为 key:value 的形式
                let arr = paramsArr[i].split('=')
                obj[arr[0]] = arr[1];
            }
            let param = {
                id: obj.uid,
                password: obj.pwd
            };
            axios.post("/user/verification", param).then((res) => {
                if (res.data.flag) {
                    this.user = res.data.data;
                    this.main_getTransferRecordList();
                    this.main_getLinkIdOptionList();
                    this.main_getHandleStatusOptionList();
                    this.main_getFirstReviewStatusOptionList();
                    this.main_getSecondReviewStatusOptionList();
                    this.main_getOnlineAvailableOptionList();
                }
            });

            // axios.post("/sales/deliveryNote/creatDeliveryNotePDF", param).then((res) => {

            // });
        },

        watch: {
            report_transportReportDetailsVisible(newVal) {
                if (newVal) {
                    this.$nextTick(() => {
                        this.$refs.report_requirementDataTableRef.$el
                            .querySelector(".el-table__body-wrapper")
                            .addEventListener("scroll", this.utils_syncScrollX, {
                                passive: true
                            });
                        this.utils_syncScrollX();

                        // 添加自动填充到期日的逻辑
                        this.autoFillDateFields();
                    });
                }
                if (!newVal) {
                    // 关闭所有通知
                    this.$notify.closeAll();
                }
            },
        },

        methods: {
            // 主页面_获取出库传递单列表
            main_getTransferRecordList() {
                this.main_tableLoading = true;
                if (this.main_transferRecordQuery.queryDate == null) {
                    this.main_transferRecordQuery.queryDate = ["", ""];
                }
                this.main_transferRecordQuery.startDate = this.main_transferRecordQuery.queryDate[0];
                this.main_transferRecordQuery.endDate = this.main_transferRecordQuery.queryDate[1];
                axios.post("/transferRecord/getTransferRecordPage", this.main_transferRecordQuery).then((res) => {
                    if (res.data.flag) {
                        this.main_transferRecordQuery.current = Number(res.data.data.current);
                        this.main_transferRecordQuery.size = Number(res.data.data.size);
                        this.main_transferRecordQuery.total = Number(res.data.data.total);
                        this.main_transferRecordList = res.data.data.records;
                    } else {
                        this.main_transferRecordList = [];
                        this.$message.error("出库传递单查询失败");
                    }
                }).finally(() => {
                    this.main_tableLoading = false;
                });
            },

            // 主页面_重置出库传递单查询条件
            main_refreshSearch() {
                this.main_transferRecordQuery = {
                    current: 1,//当前页码
                    size: 10,//每页显示的记录数
                    total: 0,//总记录数

                    linkId: [],
                    customerName: "",
                    productName: "",
                    productionBatch: "", // 添加生产批号查询
                    qualityInspectionBatch: "", // 添加质检批号查询
                    handleStatus: "",
                    firstReviewStatus: "",
                    secondReviewStatus: "",

                    queryDate: ["", ""],// 查询日期（无需接收）
                    startDate: "",// 起始日期
                    endDate: "",// 截止日期
                };
                this.main_getTransferRecordList();
            },

            // 主页面_打开工作人员设置窗口
            main_openStaffOptionDialog() {
                axios.post("/transferRecord/getStaff", this.user).then((res) => {
                    if (res.data.flag) {
                        this.staff = res.data.data;
                        this.main_staffOptionVisible = true;
                    } else {
                        this.$message.error("工作人员获取失败");
                    }
                });
            },

            // 主页面_设置工作人员
            main_setStaff() {
                this.staff.userId = this.user.id;
                axios.post("/transferRecord/setStaff", this.staff).then((res) => {
                    if (res.data.flag) {
                        this.$message.success("工作人员设置成功");
                        this.main_staffOptionVisible = false;
                    } else {
                        this.$message.error("工作人员设置失败");
                    }
                });
            },

            // 主页面_出库传递单列表页面切换
            main_handleCurrentChange(current) {
                this.main_transferRecordQuery.current = current;
                this.main_getTransferRecordList()
            },

            // 主页面_打开出库传递单详情窗口
            main_openTransferRecordDetailsDialog(row) {
                axios.post("/transferRecord/getTransferRecordDetails", row).then((res) => {
                    if (res.data.flag) {
                        this.details_transportReportDataList = [];
                        this.details_transferRecordTitle = row.customerName + "-" + row.productName;
                        this.details_currentTransferRecord = row;
                        this.details_transferRecordDetails = res.data.data;
                        this.details_transferRecordDetailsVisible = true;
                        this.$nextTick(() => {
                            this.details_transferRecordDetails.productionAndQualityDataList.forEach(productionAndQualityData => {
                                if (productionAndQualityData.qualityInspectionData.length === 1) {
                                    this.$refs[productionAndQualityData.transferRecordList[0].outboundProductionBatch][0].toggleAllSelection();
                                }
                            });
                        });
                    } else {
                        this.$message.error("出库传递单详情查询失败");
                    }
                });
            },

            // 主页面_打开随车质检单预览窗口
            main_openTransportReportPreviewDialog(row) {
                axios.post("/transferRecord/getTransportReportPreview", row).then((res) => {
                    if (res.data.flag) {
                        this.preview_currentTransferRecord = row;
                        this.preview_transportReportPreviewUrls = res.data.data.map(svg =>
                            'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg)
                        );
                        this.preview_transportReportPreviewVisible = true;
                    } else {
                        this.$message.error("出库传递单详情查询失败");
                    }
                });
            },

            // 主页面_下载随车质检单
            main_downloadTransportReport(row, lang) {
                window.location.href = "/transferRecord/downloadTransportReport/" + row.linkId + "/" + row.outboundNumber + "/" + row.productName + "/" + lang + "/" + this.user.id;
            },

            // 主页面_重置随车质检单
            main_resetTransportReport(row) {
                this.$confirm("是否重置随车质检单?", "提示", { type: "warning" }).then(() => {
                    axios.post("/transferRecord/resetTransportReport", row).then((res) => {
                        if (res.data.flag) {
                            this.$message.success("随车质检单重置成功");
                            this.main_getTransferRecordList();
                        } else {
                            this.$message.error("随车质检单重置失败");
                        }
                    });
                }).catch(() => {
                    this.$message.info("操作取消");
                })
            },

            // 预览_随车质检单审核
            preview_reportReview(number, flag) {
                let reportReviewDTO = {
                    number: number,
                    flag: flag,
                    transferRecord: this.preview_currentTransferRecord
                }
                axios.post("/transferRecord/reportReview", reportReviewDTO).then((res) => {
                    if (res.data.flag) {
                        this.$message.success("随车质检单审核成功");
                        this.preview_transportReportPreviewVisible = false;
                        this.main_getTransferRecordList();
                    } else {
                        this.$message.error("随车质检单审核失败");
                    }
                });
            },

            // 出库传递单详情_获取选中行
            details_handleSelectionChange(productionAndQualityData, selectedQualityData) {
                let selectedRow = "";
                if (selectedQualityData.length > 1) {
                    selectedRow = selectedQualityData[1];
                    this.$refs[productionAndQualityData.transferRecordList[0].outboundProductionBatch][0].toggleRowSelection(selectedQualityData[0]);
                } else {
                    selectedRow = selectedQualityData[0];
                }
                // 检查是否已存在相同的生产批号
                const existingIndex = this.details_transportReportDataList.findIndex(item =>
                    item.outboundProductionBatch === productionAndQualityData.transferRecordList[0].outboundProductionBatch
                );

                if (selectedQualityData.length === 0) {
                    // 如果未选择任何行，则删除对应生产批号的数据
                    if (existingIndex !== -1) {
                        this.details_transportReportDataList.splice(existingIndex, 1);
                    }
                } else {
                    if (existingIndex !== -1) {
                        // 替换已存在的 qualityData
                        this.details_transportReportDataList[existingIndex].qualityData = selectedRow;
                    } else {
                        // console.log(productionAndQualityData.transferRecordList[0]);
                        // console.log(productionAndQualityData);
                        // 添加新行
                        this.details_transportReportDataList.push({
                            // productionBatch: productionAndQualityData.transferRecordList[0].productionBatch,
                            outboundProductionBatch: productionAndQualityData.transferRecordList[0].outboundProductionBatch,
                            quantity: productionAndQualityData.transferRecordList[0].quantity,
                            qualityData: selectedRow
                        });
                    }
                }
            },

            // 出库传递单详情_创建随车质检单
            details_creatTransportReport() {
                if (this.details_transportReportDataList.length < this.details_transferRecordDetails.productionAndQualityDataList.length) {
                    this.$message.warning("请选择需要参照的质检记录");
                    return;
                }
                this.report_inspectionConclusion = true;
                let transportReportCreatDTO = {};
                transportReportCreatDTO.user = this.user;
                transportReportCreatDTO.groupRequirement = this.details_transferRecordDetails.groupRequirement;

                transportReportCreatDTO.transferRecord = this.details_currentTransferRecord;
                // 包装规格
                transportReportCreatDTO.transferRecord.productionPackagingSpecification = this.details_transferRecordDetails.productionAndQualityDataList[0].transferRecordList[0].productionPackagingSpecification;
                // 执行标准
                transportReportCreatDTO.transferRecord.executionStandard = this.details_transferRecordDetails.productionAndQualityDataList[0].transferRecordList[0].executionStandard;
                transportReportCreatDTO.requirementColumns = this.details_transferRecordDetails.requirementColumns;
                transportReportCreatDTO.requirementData = this.details_transferRecordDetails.requirementData;
                transportReportCreatDTO.transportReportCreatDataList = this.details_transportReportDataList;

                let differentCustomer = false;
                let mismatchedData = []; // 收集不匹配的数据
                transportReportCreatDTO.transportReportCreatDataList.forEach(data => {
                    // 如果质检客户名称为"预生产"，则不视为不匹配
                    if (data.qualityData.customerName !== this.details_currentTransferRecord.customerName &&
                        data.qualityData.customerName !== "预生产") {
                        differentCustomer = true;
                        mismatchedData.push({
                            qualityInspectionBatch: data.qualityData.qualityInspectionBatch || '-',
                            qualityCustomer: data.qualityData.customerName,
                            outboundCustomer: this.details_currentTransferRecord.customerName
                        });
                    }
                })

                if (differentCustomer) {
                    // 显示客户不匹配弹窗
                    this.customerMismatch_tableData = mismatchedData;
                    this.customerMismatch_transportReportCreatDTO = transportReportCreatDTO;
                    this.customerMismatch_dialogVisible = true;
                } else {
                    axios.post("/transferRecord/getProductionBatchOutboundRecord", transportReportCreatDTO).then((res) => {
                        if (res.data.flag) {
                            this.$confirm('生产批号存在历史出库记录，是否使用历史检测数据？', '提示', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                transportReportCreatDTO = res.data.data;
                                axios.post("/transferRecord/creatTransportReport", transportReportCreatDTO).then((res) => {
                                    if (res.data.flag) {
                                        this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                        this.report_requirementColumns = res.data.data.requirementColumns;
                                        this.report_requirementData = res.data.data.requirementData;
                                        this.report_transportReportColumns = res.data.data.requirementColumns;
                                        this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                        this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                        this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                        // 添加通知显示
                                        if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                            let message = '';
                                            if (res.data.data.groupRemark) {
                                                message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                            }
                                            if (res.data.data.companyRemark) {
                                                message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                            }

                                            this.$notify({
                                                title: '备注信息',
                                                message: message,
                                                dangerouslyUseHTMLString: true,
                                                type: 'warning',
                                                duration: 0,
                                                position: 'top-right',
                                                customClass: 'wide-notification'
                                            });
                                        }

                                        this.report_transportReportDetailsVisible = true;

                                        // 自动填充到期日
                                        this.$nextTick(() => {
                                            this.autoFillDateFields();
                                        });
                                    } else {
                                        this.$message.error(res.data.msg);
                                    }
                                });
                            }).catch(() => {
                                axios.post("/transferRecord/creatTransportReport", transportReportCreatDTO).then((res) => {
                                    if (res.data.flag) {
                                        this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                        this.report_requirementColumns = res.data.data.requirementColumns;
                                        this.report_requirementData = res.data.data.requirementData;
                                        this.report_transportReportColumns = res.data.data.requirementColumns;
                                        this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                        this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                        this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                        if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                            let message = '';
                                            if (res.data.data.groupRemark) {
                                                message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                            }
                                            if (res.data.data.companyRemark) {
                                                message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                            }

                                            this.$notify({
                                                title: '备注信息',
                                                message: message,
                                                dangerouslyUseHTMLString: true,
                                                type: 'warning',
                                                duration: 0,
                                                position: 'top-right',
                                                customClass: 'wide-notification'
                                            });
                                        }

                                        this.report_transportReportDetailsVisible = true;

                                        // 自动填充到期日
                                        this.$nextTick(() => {
                                            this.autoFillDateFields();
                                        });
                                    } else {
                                        this.$message.error(res.data.msg);
                                    }
                                });
                            });
                        } else {
                            axios.post("/transferRecord/creatTransportReport", transportReportCreatDTO).then((res) => {
                                if (res.data.flag) {
                                    this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                    this.report_requirementColumns = res.data.data.requirementColumns;
                                    this.report_requirementData = res.data.data.requirementData;
                                    this.report_transportReportColumns = res.data.data.requirementColumns;
                                    this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                    this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                    this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                    // 添加通知显示
                                    if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                        let message = '';
                                        if (res.data.data.groupRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                        }
                                        if (res.data.data.companyRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                        }

                                        this.$notify({
                                            title: '备注信息',
                                            message: message,
                                            dangerouslyUseHTMLString: true,
                                            type: 'warning',
                                            duration: 0,
                                            position: 'top-right',
                                            customClass: 'wide-notification'
                                        });
                                    }

                                    this.report_transportReportDetailsVisible = true;

                                    // 自动填充到期日
                                    this.$nextTick(() => {
                                        this.autoFillDateFields();
                                    });
                                } else {
                                    this.$message.error(res.data.msg);
                                }
                            });
                        }
                    });
                }
            },

            details_spanMethod({ row, column, rowIndex, columnIndex }, item) {
                // console.log({row, column, rowIndex, columnIndex});
                // console.log(item);
                const arr = this.getSpan(item);
                if (columnIndex < 5 && columnIndex > 0) {
                    const row = arr[rowIndex].row;
                    const col = arr[rowIndex].col;
                    return {
                        rowspan: row,
                        colspan: col
                    };
                }
            },

            getSpan(list) {
                // console.log(list)
                const newArr = [];
                const obj = {};
                for (let i = 0; i < list.length; i++) {
                    if (i === 0) {
                        obj.row = 1;
                        obj.col = 1;
                        newArr.push(obj);
                    } else {
                        if (list[i].production_productionBatch === list[i - 1].production_productionBatch) {
                            newArr.push({ row: 0, col: 0 });
                            const index = list.findIndex(item => {
                                return item.production_productionBatch === list[i - 1].production_productionBatch;
                            });
                            newArr[index].row++;
                        } else {
                            newArr.push({ row: 1, col: 1 });
                        }
                    }
                }
                return newArr;
            },

            // 随车质检单_保存随车质检单
            report_saveTransportReport() {
                // 获取所有 informationEditInput 输入框的数量
                let informationInputs = this.$refs.report_formRef.$el.querySelectorAll('.informationEditInput');
                let informationMaxIndexCount = Array.from(informationInputs).filter(input => {
                    return input.classList.contains('maxIndex1'); // 检查是否包含 maxIndex1 类
                }).length;
                if (informationMaxIndexCount > 0) {
                    this.$alert('存在未填写的质检单信息，请检查', '提示', {
                        confirmButtonText: '确定',
                        type: 'error'
                    });
                    return;
                }

                // 获取所有 inspectionDataEditInput 输入框的数量
                let inputs = this.$refs.report_inspectionDataTableRef.$el.querySelectorAll('.inspectionDataEditInput');
                let maxIndexCount = Array.from(inputs).filter(input => {
                    return input.classList.contains('maxIndex1'); // 检查是否包含 maxIndex1 类
                }).length;
                if (maxIndexCount > 0) {
                    this.$alert('存在未填写或超出指标范围的检测数据，请检查', '提示', {
                        confirmButtonText: '确定',
                        type: 'error'
                    });
                    return;
                }

                let transportReportDTO = {};
                transportReportDTO.groupRequirement = this.details_transferRecordDetails.groupRequirement;
                transportReportDTO.transportQualityInspectionReport = this.report_transportQualityInspectionReport;
                transportReportDTO.transferRecord = this.details_currentTransferRecord;
                transportReportDTO.requirementColumns = this.report_requirementColumns;
                transportReportDTO.requirementData = this.report_requirementData;
                transportReportDTO.requirementColumns = this.report_transportReportColumns;
                transportReportDTO.qualityInspectionData = this.report_qualityInspectionData;

                axios.post("/transferRecord/getProductionBatchInspectionData", transportReportDTO).then((res) => {
                    if (res.data.flag) {
                        this.fullscreenLoading = true;
                        axios.post("/transferRecord/saveTransportReport", transportReportDTO).then((res) => {
                            if (res.data.flag) {
                                // this.preview_transportReportPreviewUrls = res.data.data.map(url => "data:image/png;base64," + url)
                                // this.preview_transportReportPreviewUrl = "data:image/png;base64," + res.data.msg;
                                this.preview_transportReportPreviewUrls = res.data.data.map(svg =>
                                    'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(svg)
                                )
                                this.preview_currentTransferRecord = this.details_currentTransferRecord;
                                this.details_transferRecordDetailsVisible = false;
                                this.report_transportReportDetailsVisible = false;
                                this.preview_transportReportPreviewVisible = true;
                            } else {
                                this.$message.error(res.data.msg);
                            }
                        }).finally(() => {
                            this.main_getTransferRecordList();
                            this.fullscreenLoading = false;
                        });
                    } else {
                        this.$alert('检测数据与其他随车质检单中的其他生产批号对应的检测数据完全相同，请调整数据后重试', '提示', {
                            confirmButtonText: '确定',
                            type: 'error'
                        });
                    }
                });
            },

            // 随车质检单_检测项目结果判断
            report_resultJudgment(data, item) {
                // 检查是否为特殊指标
                if (this.report_requirementData[0] && this.report_requirementData[0][item]) {
                    if (this.utils_isSpecialIndicator(this.report_requirementData[0][item])) {
                        console.log('Special indicator detected, skipping range validation for item:', item);
                        return; // 特殊指标跳过验证
                    }
                }

                let resultJudgmentDTO = {};
                resultJudgmentDTO.data = data;
                resultJudgmentDTO.item = item;
                resultJudgmentDTO.requirement = this.report_requirementData[0];
                axios.post("/transferRecord/resultJudgment", resultJudgmentDTO).then((res) => {
                    if (!res.data.flag) {
                        this.$confirm('检测数据超出指标范围，请检查', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {

                        }).catch(() => {

                        });
                    }
                })
            },

            report_getCommonBackgroundColorClass(data) {
                if (!data || data === '') {
                    return 'maxIndex1';
                }
            },

            report_getBackgroundColorClass(data, item) {
                // 1. 首先检查item是否存在于requirementData中
                if (!this.report_requirementData[0] || !(item in this.report_requirementData[0])) {
                    return ''; // 如果该列不需要验证，返回空字符串不添加背景色
                }

                // 2. 确保requirement存在
                if (!this.report_requirementData[0][item]) {
                    return ''; // 如果没有对应的requirement，返回空字符串
                }

                // 3. 特殊指标处理：如果是特殊指标，跳过验证
                if (this.utils_isSpecialIndicator(this.report_requirementData[0][item])) {
                    // 对于特殊指标，只检查是否为空
                    if (!data || data === '') {
                        return 'maxIndex1';
                    }
                    return ''; // 特殊指标有数据就认为合格，不添加背景色
                }

                if (!data || data === '' || (typeof data === 'string' && data.includes('暂无'))) {
                    return 'maxIndex1';
                }

                let resultJudgmentDTO = {
                    data: data,
                    item: item,
                    requirement: this.report_requirementData[0]
                };

                if (!isNaN(parseFloat(data)) && isFinite(data)) {
                    if (this.utils_isQualified(item, resultJudgmentDTO.requirement[item], data) < 1) {
                        return 'maxIndex1';
                    }
                }

                // 外观特殊处理
                if (item.includes("外观")) {
                    const keywords = ["粉", "粒", "片", "板", "固体", "液体"];

                    // 找出指标中包含的所有关键词
                    const foundKeywords = keywords.filter(keyword =>
                        resultJudgmentDTO.requirement[item].includes(keyword)
                    );

                    // 如果指标中包含关键词
                    if (foundKeywords.length > 0) {
                        // 检查数据是否包含指标中已有的任意一个关键词
                        const resultHasMatchingKeyword = foundKeywords.some(keyword =>
                            data.toString().includes(keyword)
                        );

                        if (!resultHasMatchingKeyword) {
                            return 'maxIndex1';
                        }
                    }
                }
                return '';
            },

            // 主页面_获取公司选项列表
            main_getLinkIdOptionList() {
                this.main_linkIdOptionList = [
                    {
                        label: "尚舜",
                        value: 3
                    },
                    {
                        label: "恒舜",
                        value: 4
                    },
                    {
                        label: "潍坊",
                        value: 7
                    },
                    {
                        label: "永舜",
                        value: 8
                    }
                ];
            },

            // 主页面_获取处理状态选项列表
            main_getHandleStatusOptionList() {
                this.main_handleStatusOptionList = [
                    {
                        label: "未处理",
                        value: 0
                    },
                    {
                        label: "已处理",
                        value: 1
                    }
                ];
            },

            // 主页面_获取初审状态选项列表
            main_getFirstReviewStatusOptionList() {
                this.main_firstReviewStatusOptionList = [
                    {
                        label: "未初审",
                        value: -1
                    },
                    {
                        label: "初审通过",
                        value: 1
                    },
                    {
                        label: "初审否决",
                        value: 0
                    }
                ];
            },

            // 主页面_获取复审状态选项列表
            main_getSecondReviewStatusOptionList() {
                this.main_secondReviewStatusOptionList = [
                    {
                        label: "未复审",
                        value: -1
                    },
                    {
                        label: "复审通过",
                        value: 1
                    },
                    {
                        label: "复审否决",
                        value: 0
                    }
                ];
            },

            // 主页面_获取开单状态选项列表
            main_getOnlineAvailableOptionList() {
                this.main_onlineAvailableOptionList = [
                    { label: "可线上开单", value: 1 },
                    { label: "指标错误", value: 2 },
                    { label: "指标错误-待确认", value: 3 },
                    { label: "模板错误", value: 0 },
                ];
            },

            // 工具_绑定2个表格的横向滚动条
            utils_syncScrollX() {
                const inspectionTable = this.$refs.report_requirementDataTableRef.$el.querySelector('.el-table__body-wrapper');
                const requirementTable = this.$refs.report_inspectionDataTableRef.$el.querySelector('.el-table__body-wrapper');

                inspectionTable.addEventListener('scroll', () => {
                    requirementTable.scrollLeft = inspectionTable.scrollLeft;
                });

                requirementTable.addEventListener('scroll', () => {
                    inspectionTable.scrollLeft = requirementTable.scrollLeft;
                });
            },

            // 工具_判断是否为特殊指标（只有单位信息，没有数值范围）
            utils_isSpecialIndicator(requirement) {
                if (!requirement || requirement.trim() === '') {
                    return false;
                }

                requirement = String(requirement).trim();

                // 常见的单位模式（只有单位，没有数值）
                const unitOnlyPatterns = [
                    /^g\/L$/i,           // g/L
                    /^mg\/L$/i,          // mg/L
                    /^kg\/m³$/i,         // kg/m³
                    /^%$/,               // %
                    /^℃$/,               // ℃
                    /^°C$/i,             // °C
                    /^N$/,               // N
                    /^CN$/,              // CN
                    /^g$/,               // g
                    /^kg$/i,             // kg
                    /^ml$/i,             // ml
                    /^L$/,               // L
                    /^mm$/i,             // mm
                    /^cm$/i,             // cm
                    /^m$/,               // m
                    /^s$/,               // s
                    /^min$/i,            // min
                    /^h$/,               // h
                    /^Pa$/i,             // Pa
                    /^MPa$/i,            // MPa
                    /^bar$/i,            // bar
                    /^ppm$/i,            // ppm
                    /^ppb$/i             // ppb
                ];

                // 检查是否匹配纯单位模式
                const isUnitOnly = unitOnlyPatterns.some(pattern => pattern.test(requirement));

                // 如果是纯单位，则为特殊指标
                if (isUnitOnly) {
                    return true;
                }

                // 检查是否不包含数字（原有逻辑保持）
                if (!/[0-9]/.test(requirement)) {
                    return true;
                }

                return false;
            },

            // 工具_判断检测项目是否合格
            utils_isQualified(item, requirement, result) {
                try {
                    // 参数有效性检查 - 增加更详细的日志
                    if (!item || item.trim() === '') {
                        console.warn('Invalid item parameter:', item);
                        return 0;
                    }

                    if (!requirement || requirement.trim() === '') {
                        console.warn('Invalid requirement parameter for item:', item, requirement);
                        return 0;
                    }

                    if (result === undefined || result === null || result.toString().trim() === '') {
                        console.warn('Invalid result parameter for item:', item, result);
                        return 0;
                    }

                    // 将requirement转换为字符串并去除首尾空格
                    requirement = String(requirement).trim();

                    // 特殊指标识别：如果是特殊指标，直接返回合格
                    if (this.utils_isSpecialIndicator(requirement)) {
                        console.log('Special indicator detected for item:', item, 'requirement:', requirement);
                        return 1;
                    }

                    // 原有的特殊情况处理（保持向后兼容）
                    if (requirement.includes("暂无") ||
                        requirement === "/" ||
                        requirement.includes("全通过") ||
                        requirement.includes("无黑点杂质")) {
                        return 1;
                    }

                    // 确保result可以被转换为数字，先去除首尾空格
                    const resultBigDecimal = Number(String(result).trim());
                    if (isNaN(resultBigDecimal)) {
                        console.warn('Result cannot be converted to number:', result);
                        return 0;
                    }

                    const min = Number.MIN_VALUE;
                    const max = Number.MAX_VALUE;

                    const pattern = /(?<symbol>[≥≤><＞＜=])?(?<value>\d+(\.\d+)?)((?<rangeSeparator>[±-])(?<maxValue>\d+(\.\d+)?))?(?<unit>[\p{L}\p{S}\p{Zs}\p{P}]*)/u;
                    const matcher = pattern.exec(requirement);

                    if (!matcher) {
                        console.warn('Pattern matching failed for requirement:', requirement);
                        return 0;
                    }

                    let { symbol, rangeSeparator, value, maxValue, unit } = matcher.groups;

                    // 数值转换检查
                    let valueNum = Number(value);
                    if (isNaN(valueNum)) {
                        console.warn('Value cannot be converted to number:', value);
                        return 0;
                    }

                    let maxValueNum = maxValue ? Number(maxValue) : Number.MAX_VALUE;
                    if (isNaN(maxValueNum)) {
                        console.warn('MaxValue cannot be converted to number:', maxValue);
                        return 0;
                    }

                    if (!symbol && !rangeSeparator) {
                        return Math.abs(valueNum - resultBigDecimal) < 0.00001 ? 1 : 0;
                    }

                    if (!symbol) {
                        if (rangeSeparator === "-") {
                            const centerValue = (valueNum + maxValueNum) / 2;
                            const deviation = Math.abs(maxValueNum - valueNum) / 2;
                            valueNum = centerValue;
                            maxValueNum = deviation;
                            symbol = "±";
                        } else if (rangeSeparator === "±") {
                            symbol = "±";
                        }
                    }

                    let newMin = min;
                    let newMax = max;

                    switch (symbol) {
                        case "≥":
                            newMin = valueNum;
                            break;
                        case "＞":
                        case ">":
                            newMin = valueNum + 0.00001;
                            break;
                        case "≤":
                            newMax = valueNum;
                            newMin = 0;
                            break;
                        case "＜":
                        case "<":
                            newMax = valueNum - 0.00001;
                            newMin = 0;
                            break;
                        case "±":
                            newMin = valueNum - maxValueNum;
                            newMax = valueNum + maxValueNum;
                            break;
                        default:
                            newMin = valueNum;
                            newMax = valueNum;
                            break;
                    }

                    // 特殊处理颗粒强度
                    if (item && item.includes("颗粒强度") &&
                        (requirement.includes("g") || requirement.includes("CN"))) {
                        newMin = Math.max(newMin, 10);
                    }

                    const greaterOrEqual = resultBigDecimal >= newMin;
                    const lessOrEqual = resultBigDecimal <= newMax;

                    return (greaterOrEqual && lessOrEqual) ? 1 : 0;
                } catch (error) {
                    console.error('Error in utils_isQualified for item:', item, error);
                    return 0;
                }
            },

            // 处理预览窗口关闭
            handlePreviewDialogClose() {
                if (this.$refs.previewCarousel) {
                    this.$refs.previewCarousel.setActiveItem(0);
                    // 重置高度为默认值
                    this.carouselHeight = '1000px';
                    this.imageHeights = []; // 清空图片高度数组
                    this.currentImageIndex = 0; // 重置当前图片索引
                }
            },

            // 主页面_改变线上状态
            main_changeOnlineAvailable(row) {
                // 在这里实现切换在线状态的逻辑
                // row 包含当前行的数据
                let transferRecordDTO = row;
                transferRecordDTO.user = this.user;
                axios.post("/transferRecord/changeOnlineAvailable", transferRecordDTO).then((res) => {
                    if (res.data.flag) {
                        // row.onlineAvailable = res.data.data.onlineAvailable;
                        this.main_getTransferRecordList();
                        this.$message.success("线上开单状态更新成功");
                    } else {
                        this.$message.error("线上开单状态更新失败");
                    }
                });
            },

            getOnlineAvailableTooltip(status) {
                switch (status) {
                    case 1:
                        return '可在线上开单';
                    case 2:
                        return '不可在线上开单（指标错误）';
                    case 3:
                        return '不可在线上开单（指标错误-待确认）';
                    case 0:
                        return '不可在线上开单（模版错误）';
                    case null:
                        return '未标识';
                    default:
                        return '';
                }
            },

            // 客户不匹配_确认
            customerMismatch_confirm() {
                // 关闭弹窗
                this.customerMismatch_dialogVisible = false;

                // 执行后续操作
                axios.post("/transferRecord/getProductionBatchOutboundRecord", this.customerMismatch_transportReportCreatDTO).then((res) => {
                    if (res.data.flag) {
                        this.$confirm('生产批号存在历史出库记录，是否使用历史检测数据？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            this.customerMismatch_transportReportCreatDTO = res.data.data;
                            axios.post("/transferRecord/creatTransportReport", this.customerMismatch_transportReportCreatDTO).then((res) => {
                                if (res.data.flag) {
                                    this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                    this.report_requirementColumns = res.data.data.requirementColumns;
                                    this.report_requirementData = res.data.data.requirementData;
                                    this.report_transportReportColumns = res.data.data.requirementColumns;
                                    this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                    this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                    this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                    // 添加通知显示
                                    if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                        let message = '';
                                        if (res.data.data.groupRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                        }
                                        if (res.data.data.companyRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                        }

                                        this.$notify({
                                            title: '备注信息',
                                            message: message,
                                            dangerouslyUseHTMLString: true,
                                            type: 'warning',
                                            duration: 0,
                                            position: 'top-right',
                                            customClass: 'wide-notification'
                                        });
                                    }

                                    this.report_transportReportDetailsVisible = true;

                                    // 自动填充到期日
                                    this.$nextTick(() => {
                                        this.autoFillDateFields();
                                    });
                                } else {
                                    this.$message.error(res.data.msg);
                                }
                            });
                        }).catch(() => {
                            axios.post("/transferRecord/creatTransportReport", this.customerMismatch_transportReportCreatDTO).then((res) => {
                                if (res.data.flag) {
                                    this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                    this.report_requirementColumns = res.data.data.requirementColumns;
                                    this.report_requirementData = res.data.data.requirementData;
                                    this.report_transportReportColumns = res.data.data.requirementColumns;
                                    this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                    this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                    this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                    if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                        let message = '';
                                        if (res.data.data.groupRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                        }
                                        if (res.data.data.companyRemark) {
                                            message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                        }

                                        this.$notify({
                                            title: '备注信息',
                                            message: message,
                                            dangerouslyUseHTMLString: true,
                                            type: 'warning',
                                            duration: 0,
                                            position: 'top-right',
                                            customClass: 'wide-notification'
                                        });
                                    }

                                    this.report_transportReportDetailsVisible = true;

                                    // 自动填充到期日
                                    this.$nextTick(() => {
                                        this.autoFillDateFields();
                                    });
                                } else {
                                    this.$message.error(res.data.msg);
                                }
                            });
                        });
                    } else {
                        axios.post("/transferRecord/creatTransportReport", this.customerMismatch_transportReportCreatDTO).then((res) => {
                            if (res.data.flag) {
                                this.report_transportQualityInspectionReport = res.data.data.transportQualityInspectionReport;
                                this.report_requirementColumns = res.data.data.requirementColumns;
                                this.report_requirementData = res.data.data.requirementData;
                                this.report_transportReportColumns = res.data.data.requirementColumns;
                                this.report_qualityInspectionData = res.data.data.qualityInspectionData;
                                this.report_inspectorLabelList = res.data.data.inspectorLabelList;
                                this.report_transportQualityInspectionReport.inspector = this.report_inspectorLabelList[0].value;

                                // 添加通知显示
                                if (res.data.data.groupRemark || res.data.data.companyRemark) {
                                    let message = '';
                                    if (res.data.data.groupRemark) {
                                        message += `<span style="font-weight: bold; font-size: 14px;">集团备注:</span><br>${res.data.data.groupRemark}<br>`;
                                    }
                                    if (res.data.data.companyRemark) {
                                        message += `<span style="font-weight: bold; font-size: 14px;">公司备注:</span><br>${res.data.data.companyRemark}`;
                                    }

                                    this.$notify({
                                        title: '备注信息',
                                        message: message,
                                        dangerouslyUseHTMLString: true,
                                        type: 'warning',
                                        duration: 0,
                                        position: 'top-right',
                                        customClass: 'wide-notification'
                                    });
                                }

                                this.report_transportReportDetailsVisible = true;

                                // 自动填充到期日
                                this.$nextTick(() => {
                                    this.autoFillDateFields();
                                });
                            } else {
                                this.$message.error(res.data.msg);
                            }
                        });
                    }
                });
            },

            // 自动填充到期日、有效期、保质期
            autoFillDateFields() {
                // 需要自动填充的日期字段列表
                const dateFields = ["到期日", "有效期", "保质期"];

                // 遍历每个日期字段
                dateFields.forEach(fieldName => {
                    // 检查是否有该字段列
                    const fieldIndex = this.report_transportReportColumns.findIndex(item => item.label === fieldName);

                    // 如果存在该字段列且检测指标表格有数据
                    if (fieldIndex !== -1 && this.report_requirementData.length > 0) {
                        // 从检测指标表格中获取该字段的值
                        const fieldValue = this.report_requirementData[0][fieldName];

                        // 如果有值，填充到每一行检测数据的对应字段
                        if (fieldValue) {
                            this.report_qualityInspectionData.forEach(row => {
                                // 避免覆盖已有数据
                                if (!row[fieldName]) {
                                    this.$set(row, fieldName, fieldValue);
                                }
                            });
                        }
                    }
                });
            },

            handleCarouselChange(index) {
                this.currentImageIndex = index;
                // 如果已经有了这个索引的高度，则使用它
                if (this.imageHeights[index]) {
                    this.carouselHeight = this.imageHeights[index] + 'px';
                } else {
                    // 否则设置一个默认高度
                    this.carouselHeight = '1000px';
                }
            },

            onImageLoad(event, index) {
                // 获取图片原始尺寸
                const img = event.target;

                if (img.naturalWidth && img.naturalHeight) {
                    // 计算等比缩放后的高度
                    const containerWidth = this.$refs.previewCarousel.$el.clientWidth;
                    const scaleFactor = containerWidth / img.naturalWidth;
                    const scaledHeight = img.naturalHeight * scaleFactor;

                    // 设置最小高度，避免图片太小
                    const heightToUse = Math.max(scaledHeight, 400);

                    // 存储图片高度
                    this.$set(this.imageHeights, index, heightToUse);

                    // 如果是当前显示的图片，则更新carousel高度
                    if (index === this.currentImageIndex) {
                        this.carouselHeight = heightToUse + 'px';
                    }
                }
            },

            // 批号信息表格合并方法
            batchInfoSpanMethod({ row, column, rowIndex, columnIndex }, batchInfoList) {
                // 仅对生产批号列进行合并（第一列）
                if (columnIndex === 0) {
                    // 获取合并信息
                    const arr = this.getBatchInfoSpan(batchInfoList);
                    const rowSpan = arr[rowIndex].row;
                    const colSpan = arr[rowIndex].col;
                    return {
                        rowspan: rowSpan,
                        colspan: colSpan
                    };
                }
            },

            // 计算批号信息表格合并信息
            getBatchInfoSpan(list) {
                const newArr = [];
                for (let i = 0; i < list.length; i++) {
                    if (i === 0) {
                        // 第一行默认显示
                        newArr.push({ row: 1, col: 1 });
                    } else {
                        // 判断当前行与前一行的生产批号是否相同
                        if (list[i].productionBatch === list[i-1].productionBatch) {
                            // 如果相同，当前行不显示（rowspan=0）
                            newArr.push({ row: 0, col: 0 });
                            // 查找第一次出现该批号的行
                            const index = list.findIndex(item => {
                                return item.productionBatch === list[i].productionBatch;
                            });
                            // 增加第一次出现行的rowspan值
                            newArr[index].row++;
                        } else {
                            // 不同批号，正常显示
                            newArr.push({ row: 1, col: 1 });
                        }
                    }
                }
                return newArr;
            },

            // 排序批号信息，确保相同生产批号的记录相邻
            sortBatchInfo(batchInfo) {
                if (!batchInfo || batchInfo.length === 0) return [];

                // 对批号信息进行排序，按生产批号排序
                return [...batchInfo].sort((a, b) => {
                    if (a.productionBatch && b.productionBatch) {
                        return a.productionBatch.localeCompare(b.productionBatch);
                    }
                    return 0;
                });
            },

            // 导出功能_打开导出对话框
            main_openExportDialog() {
                this.export_dialogVisible = true;
                this.export_loadGroupOptions();
                this.export_loadCustomerOptions();
                this.export_loadProductCategoryOptions();
            },

            // 导出功能_加载集团选项
            export_loadGroupOptions() {
                axios.get('/transferRecord/getGroupOptions')
                    .then((res) => {
                        if (res.data.flag) {
                            this.export_groupOptions = res.data.data;
                            this.export_filteredGroupOptions = res.data.data; // 初始化过滤选项
                        } else {
                            this.$message.error('获取集团选项失败：' + res.data.msg);
                            this.export_groupOptions = [];
                            this.export_filteredGroupOptions = [];
                        }
                    })
                    .catch((error) => {
                        console.error('获取集团选项错误：', error);
                        this.$message.error('获取集团选项失败');
                        this.export_groupOptions = [];
                        this.export_filteredGroupOptions = [];
                    });
            },

            // 导出功能_过滤集团选项（防抖处理）
            export_filterGroupOptions: (function() {
                let timeout;
                return function(keyword) {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        if (keyword) {
                            this.export_filteredGroupOptions = this.export_groupOptions.filter(option =>
                                option.label.toLowerCase().includes(keyword.toLowerCase())
                            );
                        } else {
                            this.export_filteredGroupOptions = this.export_groupOptions;
                        }
                    }, 300); // 300ms 防抖延迟
                };
            })(),

            // 导出功能_加载客户选项
            export_loadCustomerOptions(keyword = '') {
                const params = {
                    groupNumber: this.export_form.groupNumber || null,
                    keyword: keyword || null
                };

                this.export_customerLoading = true;
                axios.get('/transferRecord/getCustomerOptions', { params })
                    .then((res) => {
                        if (res.data.flag) {
                            this.export_customerOptions = res.data.data;
                        } else {
                            this.$message.error('获取客户选项失败：' + res.data.msg);
                            this.export_customerOptions = [];
                        }
                    })
                    .catch((error) => {
                        console.error('获取客户选项错误：', error);
                        this.$message.error('获取客户选项失败');
                        this.export_customerOptions = [];
                    })
                    .finally(() => {
                        this.export_customerLoading = false;
                    });
            },

            // 导出功能_搜索客户
            export_searchCustomers(keyword) {
                if (keyword !== '') {
                    this.export_loadCustomerOptions(keyword);
                } else {
                    this.export_loadCustomerOptions();
                }
            },

            // 导出功能_加载产品类别选项
            export_loadProductCategoryOptions() {
                axios.get('/transferRecord/getProductCategoryOptions')
                    .then((res) => {
                        if (res.data.flag) {
                            this.export_productCategoryOptions = res.data.data;
                        } else {
                            this.$message.error('获取产品类别选项失败：' + res.data.msg);
                            this.export_productCategoryOptions = [];
                        }
                    })
                    .catch((error) => {
                        console.error('获取产品类别选项错误：', error);
                        this.$message.error('获取产品类别选项失败');
                        this.export_productCategoryOptions = [];
                    });
            },

            // 导出功能_集团变化事件
            export_onGroupChange() {
                // 当集团变化时，重新加载该集团下的客户
                this.export_form.customerNumbers = [];
                this.export_customerOptions = [];
                this.export_loadCustomerOptions();
            },

            // 导出功能_切换日期选择器类型
            export_toggleDatePickerType() {
                // 清空当前选择的日期范围
                this.export_form.dateRange = [];

                // 切换类型
                if (this.export_datePickerType === 'daterange') {
                    this.export_datePickerType = 'monthrange';
                } else {
                    this.export_datePickerType = 'daterange';
                }
            },

            // 导出功能_执行导出
            export_executeExport() {
                // 验证必填项 - 修改验证逻辑：只有在未选择集团时，客户选择才是必填项
                if (!this.export_form.groupNumber && (!this.export_form.customerNumbers || this.export_form.customerNumbers.length === 0)) {
                    this.$message.error('未选择集团时，客户选择为必填项');
                    return;
                }

                this.export_loading = true;

                // 处理日期范围
                let startDate = null;
                let endDate = null;

                if (this.export_form.dateRange && this.export_form.dateRange.length > 0) {
                    if (this.export_datePickerType === 'monthrange') {
                        // 月份范围：转换为该月的起始和结束日期
                        if (this.export_form.dateRange.length > 0) {
                            startDate = this.export_form.dateRange[0] + '-01'; // 月份开始日期
                        }
                        if (this.export_form.dateRange.length > 1) {
                            // 计算月份的最后一天
                            const endMonth = this.export_form.dateRange[1];
                            const year = parseInt(endMonth.split('-')[0]);
                            const month = parseInt(endMonth.split('-')[1]);
                            const lastDay = new Date(year, month, 0).getDate(); // 获取该月最后一天
                            endDate = endMonth + '-' + String(lastDay).padStart(2, '0');
                        }
                    } else {
                        // 日期范围：直接使用
                        startDate = this.export_form.dateRange[0];
                        endDate = this.export_form.dateRange.length > 1 ? this.export_form.dateRange[1] : null;
                    }
                }

                // 构建请求参数
                const exportRequest = {
                    groupNumber: this.export_form.groupNumber || null,
                    customerNumbers: this.export_form.customerNumbers.length > 0 ? this.export_form.customerNumbers : null,
                    productCategoryNumbers: this.export_form.productCategoryNumbers.length > 0 ? this.export_form.productCategoryNumbers : null,
                    startDate: startDate,
                    endDate: endDate,
                    separateByCustomer: this.export_form.separateByCustomer
                };

                // 调用导出API - 修改为文件下载方式
                axios.post('/transferRecord/exportTransportReportData', exportRequest, {
                    responseType: 'blob' // 重要：设置响应类型为blob
                })
                    .then((response) => {
                        // 创建blob对象
                        const blob = new Blob([response.data], {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        });

                        // 创建下载链接
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;

                        // 从响应头获取文件名，如果没有则使用默认名称
                        let fileName = '随车质检单数据导出.xlsx';
                        const contentDisposition = response.headers['content-disposition'];
                        if (contentDisposition) {
                            const fileNameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/);
                            if (fileNameMatch) {
                                fileName = decodeURIComponent(fileNameMatch[1]);
                            }
                        }

                        link.download = fileName;
                        document.body.appendChild(link);
                        link.click();

                        // 清理
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);

                        this.$message.success('导出成功！文件已开始下载');
                        this.export_dialogVisible = false;

                        // 重置表单
                        this.export_form = {
                            groupNumber: '',
                            customerNumbers: [],
                            productCategoryNumbers: [],
                            dateRange: [],
                            separateByCustomer: false
                        };
                    })
                    .catch((error) => {
                        console.error('导出错误：', error);
                        if (error.response && error.response.data) {
                            // 如果是blob错误响应，尝试解析错误信息
                            const reader = new FileReader();
                            reader.onload = () => {
                                try {
                                    const errorData = JSON.parse(reader.result);
                                    this.$message.error('导出失败：' + errorData.msg);
                                } catch (e) {
                                    this.$message.error('导出失败，请稍后重试');
                                }
                            };
                            reader.readAsText(error.response.data);
                        } else {
                            this.$message.error('导出失败，请稍后重试');
                        }
                    })
                    .finally(() => {
                        this.export_loading = false;
                    });
            },
        }
    })
</script>

</html>